# React + TypeScript + Vite

This template provides a minimal setup to get <PERSON><PERSON> working in Vite with HMR and some ESLint rules.

Currently, two official plugins are available:

- [@vitejs/plugin-react](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react) uses [Babel](https://babeljs.io/) for Fast Refresh
- [@vitejs/plugin-react-swc](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react-swc) uses [SWC](https://swc.rs/) for Fast Refresh

## Expanding the ESLint configuration

If you are developing a production application, we recommend updating the configuration to enable type-aware lint rules:

```js
export default tseslint.config([
  globalIgnores(['dist']),
  {
    files: ['**/*.{ts,tsx}'],
    extends: [
      // Other configs...

      // Remove tseslint.configs.recommended and replace with this
      ...tseslint.configs.recommendedTypeChecked,
      // Alternatively, use this for stricter rules
      ...tseslint.configs.strictTypeChecked,
      // Optionally, add this for stylistic rules
      ...tseslint.configs.stylisticTypeChecked,

      // Other configs...
    ],
    languageOptions: {
      parserOptions: {
        project: ['./tsconfig.node.json', './tsconfig.app.json'],
        tsconfigRootDir: import.meta.dirname,
      },
      // other options...
    },
  },
])
```

You can also install [eslint-plugin-react-x](https://github.com/Rel1cx/eslint-react/tree/main/packages/plugins/eslint-plugin-react-x) and [eslint-plugin-react-dom](https://github.com/Rel1cx/eslint-react/tree/main/packages/plugins/eslint-plugin-react-dom) for React-specific lint rules:

```js
// eslint.config.js
import reactX from 'eslint-plugin-react-x'
import reactDom from 'eslint-plugin-react-dom'

export default tseslint.config([
  globalIgnores(['dist']),
  {
    files: ['**/*.{ts,tsx}'],
    extends: [
      // Other configs...
      // Enable lint rules for React
      reactX.configs['recommended-typescript'],
      // Enable lint rules for React DOM
      reactDom.configs.recommended,
    ],
    languageOptions: {
      parserOptions: {
        project: ['./tsconfig.node.json', './tsconfig.app.json'],
        tsconfigRootDir: import.meta.dirname,
      },
      // other options...
    },
  },
])
```

# MobX Reaction 监听示例

这个项目演示了如何使用 MobX 的 `reaction` 功能来监听对象的变化。

## 功能特性

- 🎯 使用 MobX `reaction` 监听对象变化
- ⏰ 每5秒自动更新对象数据
- 🔄 支持手动触发数据更新
- 📊 在控制台打印变化前后的对象数据
- 🎨 美观的 React 界面展示

## 安装依赖

```bash
npm install
```

## 运行项目

```bash
npm run dev
```

## 使用说明

1. 启动项目后，打开浏览器开发者工具的控制台
2. 观察每5秒自动打印的对象变化信息
3. 点击"手动更新用户"按钮可以立即触发变化
4. reaction 会显示变化前后的完整对象数据

## 核心代码

### Store 定义 (`src/stores/SimpleReactionStore.ts`)

```typescript
import { makeAutoObservable, reaction } from 'mobx';

class SimpleReactionStore {
  user = {
    name: '张三',
    age: 25,
    city: '北京'
  };

  constructor() {
    makeAutoObservable(this);
    
    // 监听整个 user 对象的变化
    reaction(
      () => this.user,
      (newUser, oldUser) => {
        console.log('🎯 User 对象发生变化!');
        console.log('新用户数据:', newUser);
        console.log('旧用户数据:', oldUser);
        console.log('变化时间:', new Date().toLocaleTimeString());
      },
      {
        fireImmediately: true
      }
    );

    // 每5秒自动更新数据
    this.startAutoUpdate();
  }

  // ... 其他方法
}
```

### React 组件 (`src/components/SimpleReactionDemo.tsx`)

```typescript
import { observer } from 'mobx-react-lite';

const SimpleReactionDemo: React.FC = observer(() => {
  return (
    <div>
      <h3>当前用户信息:</h3>
      <p>姓名: {simpleReactionStore.user.name}</p>
      <p>年龄: {simpleReactionStore.user.age}</p>
      <p>城市: {simpleReactionStore.user.city}</p>
      <button onClick={() => simpleReactionStore.manualUpdate()}>
        手动更新用户
      </button>
    </div>
  );
});
```

## 技术要点

- **makeAutoObservable**: 使对象的所有属性变为可观察的
- **reaction**: 监听可观察数据的变化并执行副作用
- **fireImmediately**: 立即执行一次 reaction
- **observer**: 使 React 组件能够响应 MobX 状态变化
- **setInterval**: 定时更新数据以触发 reaction

## 项目结构

```
src/
├── stores/
│   ├── SimpleReactionStore.ts    # 主要的 reaction 示例
│   └── DataStore.ts              # 更复杂的 reaction 示例
├── components/
│   ├── SimpleReactionDemo.tsx    # 简单的演示组件
│   └── DataMonitor.tsx           # 复杂演示组件
└── App.tsx                       # 主应用组件
```

## 依赖版本

- mobx: ^6.12.0
- mobx-react-lite: ^4.0.6
- react: ^19.1.0
- typescript: ~5.8.3
