import { makeAutoObservable, reaction } from 'mobx';

interface DataObject {
  id: number;
  name: string;
  value: number;
  timestamp: Date;
}

class DataStore {
  data: DataObject = {
    id: 1,
    name: '初始数据',
    value: 100,
    timestamp: new Date()
  };

  constructor() {
    makeAutoObservable(this);
    
    // 使用 reaction 监听 data 对象的变化
    reaction(
      () => this.data,
      (newData, oldData) => {
        console.log('🔍 数据发生变化!');
        console.log('📊 新数据:', newData);
        console.log('📊 旧数据:', oldData);
        console.log('🕐 变化时间:', new Date().toLocaleTimeString());
        console.log('---');
      },
      {
        fireImmediately: true, // 立即执行一次
        equals: (prev, next) => {
          // 自定义比较函数，只有当 id 或 value 发生变化时才触发
          return prev.id === next.id && prev.value === next.value;
        }
      }
    );

    // 每5秒自动更新数据
    this.startAutoUpdate();
  }

  updateData() {
    this.data = {
      id: Math.floor(Math.random() * 1000) + 1,
      name: `数据-${Math.random().toString(36).substr(2, 5)}`,
      value: Math.floor(Math.random() * 1000),
      timestamp: new Date()
    };
  }

  private startAutoUpdate() {
    setInterval(() => {
      this.updateData();
    }, 5000);
  }

  // 手动更新数据的方法
  manualUpdate() {
    this.updateData();
  }
}

export const dataStore = new DataStore(); 