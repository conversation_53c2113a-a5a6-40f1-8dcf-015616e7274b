import { makeAutoObservable, reaction } from 'mobx';

class SimpleReactionStore {
  user = {
    name: '张三',
    age: 25,
    city: '北京'
  };

  constructor() {
    makeAutoObservable(this);
    
    // 监听整个 user 对象的变化
    reaction(
      () => this.user,
      (newUser, oldUser) => {
        console.log('🎯 User 对象发生变化!');
        console.log('新用户数据:', newUser);
        console.log('旧用户数据:', oldUser);
        console.log('变化时间:', new Date().toLocaleTimeString());
        console.log('---');
      },
      {
        fireImmediately: true
      }
    );

    // 每5秒更新用户数据
    this.startAutoUpdate();
  }

  updateUser() {
    const names = ['张三', '李四', '王五', '赵六', '钱七'];
    const cities = ['北京', '上海', '广州', '深圳', '杭州'];
    
    this.user = {
      name: names[Math.floor(Math.random() * names.length)],
      age: Math.floor(Math.random() * 50) + 18,
      city: cities[Math.floor(Math.random() * cities.length)]
    };
  }

  private startAutoUpdate() {
    setInterval(() => {
      this.updateUser();
    }, 5000);
  }

  // 手动更新方法
  manualUpdate() {
    this.updateUser();
  }
}

export const simpleReactionStore = new SimpleReactionStore(); 