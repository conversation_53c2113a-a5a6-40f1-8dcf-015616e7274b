import React from 'react';
import { observer } from 'mobx-react-lite';
import { simpleReactionStore } from '../stores/SimpleReactionStore';

const SimpleReactionDemo: React.FC = observer(() => {
  return (
    <div style={{ padding: '20px', maxWidth: '600px', margin: '0 auto' }}>
      <h2>🎯 MobX Reaction 示例</h2>
      <p>每5秒自动更新用户数据，reaction 监听对象变化并打印到控制台</p>
      
      <div style={{ 
        background: '#e3f2fd', 
        padding: '20px', 
        borderRadius: '8px',
        margin: '20px 0',
        border: '2px solid #2196f3'
      }}>
        <h3>👤 当前用户信息:</h3>
        <div style={{ marginLeft: '20px', fontSize: '16px' }}>
          <p><strong>姓名:</strong> {simpleReactionStore.user.name}</p>
          <p><strong>年龄:</strong> {simpleReactionStore.user.age}</p>
          <p><strong>城市:</strong> {simpleReactionStore.user.city}</p>
        </div>
      </div>

      <button 
        onClick={() => simpleReactionStore.manualUpdate()}
        style={{
          background: '#4caf50',
          color: 'white',
          border: 'none',
          padding: '12px 24px',
          borderRadius: '6px',
          cursor: 'pointer',
          fontSize: '16px',
          marginRight: '15px'
        }}
      >
        🔄 手动更新用户
      </button>

      <div style={{ 
        marginTop: '30px',
        padding: '20px',
        background: '#fff8e1',
        border: '1px solid #ffc107',
        borderRadius: '8px'
      }}>
        <h4>📋 使用说明:</h4>
        <ol>
          <li>打开浏览器开发者工具的控制台</li>
          <li>观察每5秒自动打印的对象变化信息</li>
          <li>点击"手动更新用户"按钮可以立即触发变化</li>
          <li>reaction 会显示变化前后的完整对象数据</li>
        </ol>
      </div>

      <div style={{ 
        marginTop: '20px',
        padding: '15px',
        background: '#f3e5f5',
        border: '1px solid #9c27b0',
        borderRadius: '6px'
      }}>
        <h4>🔧 代码要点:</h4>
        <ul>
          <li>使用 <code>makeAutoObservable(this)</code> 使对象可观察</li>
          <li>使用 <code>reaction()</code> 监听对象变化</li>
          <li>设置 <code>fireImmediately: true</code> 立即执行一次</li>
          <li>使用 <code>setInterval</code> 每5秒自动更新数据</li>
        </ul>
      </div>
    </div>
  );
});

export default SimpleReactionDemo; 