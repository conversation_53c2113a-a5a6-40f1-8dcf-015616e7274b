import React from 'react';
import { observer } from 'mobx-react-lite';
import { dataStore } from '../stores/DataStore';

const DataMonitor: React.FC = observer(() => {
  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h2>🔍 MobX Reaction 监听示例</h2>
      <p>数据每5秒自动变化一次，reaction 会监听变化并打印到控制台</p>
      
      <div style={{ 
        background: '#f5f5f5', 
        padding: '15px', 
        borderRadius: '8px',
        margin: '10px 0'
      }}>
        <h3>📊 当前数据:</h3>
        <div style={{ marginLeft: '20px' }}>
          <p><strong>ID:</strong> {dataStore.data.id}</p>
          <p><strong>名称:</strong> {dataStore.data.name}</p>
          <p><strong>数值:</strong> {dataStore.data.value}</p>
          <p><strong>时间戳:</strong> {dataStore.data.timestamp.toLocaleString()}</p>
        </div>
      </div>

      <button 
        onClick={() => dataStore.manualUpdate()}
        style={{
          background: '#007bff',
          color: 'white',
          border: 'none',
          padding: '10px 20px',
          borderRadius: '5px',
          cursor: 'pointer',
          marginRight: '10px'
        }}
      >
        🔄 手动更新数据
      </button>

      <div style={{ 
        marginTop: '20px',
        padding: '15px',
        background: '#fff3cd',
        border: '1px solid #ffeaa7',
        borderRadius: '5px'
      }}>
        <h4>💡 说明:</h4>
        <ul>
          <li>数据每5秒自动变化一次</li>
          <li>打开浏览器控制台查看 reaction 的输出</li>
          <li>reaction 会监听 data 对象的变化并打印详细信息</li>
          <li>使用自定义比较函数，只有当 id 或 value 变化时才触发</li>
        </ul>
      </div>
    </div>
  );
});

export default DataMonitor; 